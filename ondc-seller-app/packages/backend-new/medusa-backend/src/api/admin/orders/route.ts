import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  console.log(`🚀 [TENANT FILTER] === CUSTOM ORDERS ENDPOINT CALLED ===`);

  try {
    // Extract tenant ID from header
    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    console.log(`🔍 [TENANT FILTER] Processing orders request for tenant: ${tenantId}`);

    // Get query parameters
    const { limit = 50, offset = 0, fields, order = '-created_at', ...filters } = req.query;

    // Direct database connection approach
    const { Client } = require('pg');
    const client = new Client({
      connectionString: 'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
    });

    let orders: any[] = [];
    let count = 0;

    try {
      await client.connect();
      console.log(`🔗 [TENANT FILTER] Connected to database directly`);

      // Get total count for this tenant
      const countResult = await client.query(
        'SELECT COUNT(*) as total FROM "order" WHERE tenant_id = $1',
        [tenantId]
      );
      count = parseInt(countResult.rows[0]?.total || 0);
      console.log(`📊 [TENANT FILTER] Total orders for tenant ${tenantId}: ${count}`);

      // Get orders with pagination
      const result = await client.query(
        `
        SELECT 
          id, status, currency_code, total, subtotal,
          created_at, updated_at, tenant_id, metadata,
          customer_id, email
        FROM "order" 
        WHERE tenant_id = $1 
        ORDER BY created_at DESC 
        LIMIT $2 OFFSET $3
      `,
        [tenantId, parseInt(limit as string), parseInt(offset as string)]
      );

      orders = result.rows || [];
      console.log(`📋 [TENANT FILTER] Retrieved ${orders.length} orders`);
    } catch (dbError) {
      console.error('❌ [TENANT FILTER] Database error:', dbError);
      throw dbError;
    } finally {
      // Always close the database connection
      try {
        await client.end();
        console.log(`🔗 [TENANT FILTER] Database connection closed`);
      } catch (closeError) {
        console.error('❌ [TENANT FILTER] Error closing database connection:', closeError);
      }
    }

    // Return response in Medusa format
    const response = {
      orders,
      count: orders.length,
      offset: parseInt(offset as string),
      limit: parseInt(limit as string),
      _tenant: {
        id: tenantId,
        filtered: true,
        method: 'direct_db_connection',
        total_in_db: count,
      },
    };

    // Add tenant headers for debugging
    res.setHeader('X-Tenant-ID', tenantId);
    res.setHeader('X-Tenant-Filtered', 'true');
    res.setHeader('X-Orders-Count', orders.length.toString());

    console.log(`📤 [TENANT FILTER] Returning ${orders.length} orders for tenant ${tenantId}`);

    res.json(response);
  } catch (error: any) {
    console.error('❌ [TENANT FILTER] Error in orders endpoint:', error);

    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    res.status(500).json({
      error: 'Failed to fetch orders',
      message: error.message,
      tenant_id: tenantId,
      _debug: {
        error_type: 'tenant_filter_error',
        timestamp: new Date().toISOString(),
      },
    });
  }
}
