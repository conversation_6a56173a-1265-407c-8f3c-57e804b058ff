import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  console.log(`🚀 [TENANT FILTER] === CUSTOM PRODUCTS ENDPOINT CALLED ===`);
  console.log(`🚀 [TENANT FILTER] Headers:`, JSON.stringify(req.headers, null, 2));
  console.log(`🚀 [TENANT FILTER] Query:`, JSON.stringify(req.query, null, 2));

  try {
    // Extract tenant ID from header
    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    console.log(`🔍 [TENANT FILTER] Processing products request for tenant: ${tenantId}`);

    // Get query parameters
    const {
      limit = 50,
      offset = 0,
      // fields and order are available but not used in direct DB query
      // ...filters - removed unused destructuring
    } = req.query;

    // Direct database connection approach
    const { Client } = require('pg');
    const client = new Client({
      connectionString:
        process.env.DATABASE_URL ||
        process.env.POSTGRES_URL ||
        'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
    });

    let products: any[] = [];
    let count = 0;

    try {
      await client.connect();
      console.log(`🔗 [TENANT FILTER] Connected to database directly`);

      // Get total count for this tenant (exclude soft-deleted)
      const countResult = await client.query(
        'SELECT COUNT(*) as total FROM product WHERE tenant_id = $1 AND deleted_at IS NULL',
        [tenantId]
      );
      count = parseInt(countResult.rows[0]?.total || 0);
      console.log(`📊 [TENANT FILTER] Total products for tenant ${tenantId}: ${count}`);

      // First get basic products (exclude soft-deleted)
      const result = await client.query(
        `
        SELECT 
          id, title, handle, description, status, 
          created_at, updated_at, tenant_id, metadata,
          thumbnail, collection_id
        FROM product 
        WHERE tenant_id = $1 AND deleted_at IS NULL
        ORDER BY created_at DESC 
        LIMIT $2 OFFSET $3
      `,
        [tenantId, parseInt(limit as string), parseInt(offset as string)]
      );

      products = result.rows || [];
      console.log(`📦 [TENANT FILTER] Retrieved ${products.length} basic products`);

      // Now get related data for each product
      for (let i = 0; i < products.length; i++) {
        const product = products[i];

        // Get variants
        try {
          const variantsResult = await client.query(
            `
            SELECT id, title, sku, metadata, weight, width, length, height, tenant_id
            FROM product_variant 
            WHERE product_id = $1 AND tenant_id = $2
          `,
            [product.id, tenantId]
          );
          product.variants = variantsResult.rows || [];
        } catch (e) {
          console.log(`⚠️ Could not fetch variants for product ${product.id}:`, e.message);
          product.variants = [];
        }

        // Get images (no tenant_id in image table)
        try {
          const imagesResult = await client.query(
            `
            SELECT id, url, metadata
            FROM image 
            WHERE product_id = $1
          `,
            [product.id]
          );
          // Filter by tenant_id from metadata
          product.images = imagesResult.rows.filter(img => {
            try {
              const metadata =
                typeof img.metadata === 'string' ? JSON.parse(img.metadata) : img.metadata;
              return metadata && metadata.tenant_id === tenantId;
            } catch {
              return false;
            }
          });
        } catch (e) {
          console.log(`⚠️ Could not fetch images for product ${product.id}:`, e.message);
          product.images = [];
        }

        // Get options (no tenant_id in product_option table)
        try {
          const optionsResult = await client.query(
            `
            SELECT id, title, metadata
            FROM product_option 
            WHERE product_id = $1
          `,
            [product.id]
          );
          // Filter by tenant_id from metadata
          product.options = optionsResult.rows.filter(opt => {
            try {
              const metadata =
                typeof opt.metadata === 'string' ? JSON.parse(opt.metadata) : opt.metadata;
              return metadata && metadata.tenant_id === tenantId;
            } catch {
              return false;
            }
          });
        } catch (e) {
          console.log(`⚠️ Could not fetch options for product ${product.id}:`, e.message);
          product.options = [];
        }

        // Get tags
        try {
          const tagsResult = await client.query(
            `
            SELECT pt.id, pt.value
            FROM product_tag pt
            JOIN product_tags ptags ON pt.id = ptags.product_tag_id
            WHERE ptags.product_id = $1 AND pt.tenant_id = $2
          `,
            [product.id, tenantId]
          );
          product.tags = tagsResult.rows || [];
        } catch (e) {
          console.log(`⚠️ Could not fetch tags for product ${product.id}:`, e.message);
          product.tags = [];
        }

        // Get categories
        try {
          const categoriesResult = await client.query(
            `
            SELECT pc.id, pc.name, pc.handle
            FROM product_category pc
            JOIN product_category_product pcp ON pc.id = pcp.product_category_id
            WHERE pcp.product_id = $1 AND pc.tenant_id = $2
          `,
            [product.id, tenantId]
          );
          product.categories = categoriesResult.rows || [];
        } catch (e) {
          console.log(`⚠️ Could not fetch categories for product ${product.id}:`, e.message);
          product.categories = [];
        }
      }

      console.log(`📦 [TENANT FILTER] Enhanced ${products.length} products with related data`);
    } catch (dbError) {
      console.error('❌ [TENANT FILTER] Database error:', dbError);
      throw dbError;
    } finally {
      // Always close the database connection
      try {
        await client.end();
        console.log(`🔗 [TENANT FILTER] Database connection closed`);
      } catch (closeError) {
        console.error('❌ [TENANT FILTER] Error closing database connection:', closeError);
      }
    }

    // Return response in Medusa format
    const response = {
      products,
      count: products.length,
      offset: parseInt(offset as string),
      limit: parseInt(limit as string),
      // Add tenant info for debugging
      _tenant: {
        id: tenantId,
        filtered: true,
        method: 'direct_db_connection',
        total_in_db: count,
      },
    };

    // Add tenant headers for debugging
    res.setHeader('X-Tenant-ID', tenantId);
    res.setHeader('X-Tenant-Filtered', 'true');
    res.setHeader('X-Products-Count', products.length.toString());

    console.log(`📤 [TENANT FILTER] Returning response:`, {
      products_count: products.length,
      total_count: count,
      tenant_id: tenantId,
    });

    res.json(response);
  } catch (error: any) {
    console.error('❌ [TENANT FILTER] Error in products endpoint:', error);

    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    res.status(500).json({
      error: 'Failed to fetch products',
      message: error.message,
      tenant_id: tenantId,
      stack: error.stack,
      _debug: {
        error_type: 'tenant_filter_error',
        timestamp: new Date().toISOString(),
      },
    });
  }
}

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  console.log(`🚀 [TENANT FILTER] === COMPREHENSIVE PRODUCTS POST ENDPOINT CALLED ===`);
  console.log(`🚀 [TENANT FILTER] Body:`, JSON.stringify(req.body, null, 2));

  try {
    // Extract tenant ID from header
    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    console.log(`🔍 [TENANT FILTER] Creating comprehensive product for tenant: ${tenantId}`);

    // Get product data from request body
    const productData = req.body as any;

    // Inject tenant_id into the product data and all related entities
    const productWithTenant = {
      ...productData,
      tenant_id: tenantId,
      metadata: {
        ...productData.metadata,
        tenant_id: tenantId,
      },
    };

    console.log(`🏷️ [TENANT FILTER] Injected tenant_id: ${tenantId} into product data`);

    // Direct database connection approach
    const { Client } = require('pg');
    const client = new Client({
      connectionString:
        process.env.DATABASE_URL ||
        process.env.POSTGRES_URL ||
        'postgresql://strapi:strapi_password@localhost:5432/medusa_backend',
    });

    let createdProduct: any = null;
    let createdVariants: any[] = [];
    let createdImages: any[] = [];
    let createdOptions: any[] = [];

    try {
      await client.connect();
      console.log(`🔗 [TENANT FILTER] Connected to database directly`);

      // Start transaction for data consistency
      await client.query('BEGIN');

      // Generate a unique product ID
      const productId = `prod_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // 1. Insert main product with all fields
      const insertProductQuery = `
        INSERT INTO product (
          id, title, handle, description, status, 
          tenant_id, metadata, thumbnail, collection_id,
          created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW(), NOW())
        RETURNING *
      `;

      const productValues = [
        productId,
        productWithTenant.title,
        productWithTenant.handle || productWithTenant.title?.toLowerCase().replace(/\s+/g, '-'),
        productWithTenant.description || '',
        productWithTenant.status || 'draft',
        tenantId,
        JSON.stringify(productWithTenant.metadata || {}),
        productWithTenant.thumbnail || null,
        productWithTenant.collection_id || null,
      ];

      const productResult = await client.query(insertProductQuery, productValues);
      createdProduct = productResult.rows[0];
      console.log(`✅ [TENANT FILTER] Created main product: ${createdProduct.id}`);

      // 2. Insert product options
      if (productWithTenant.options && productWithTenant.options.length > 0) {
        for (const option of productWithTenant.options) {
          const optionId = `opt_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

          const insertOptionQuery = `
            INSERT INTO product_option (
              id, title, product_id, metadata, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, NOW(), NOW())
            RETURNING *
          `;

          const optionValues = [
            optionId,
            option.title,
            productId,
            JSON.stringify({ values: option.values || [], tenant_id: tenantId }),
          ];

          const optionResult = await client.query(insertOptionQuery, optionValues);
          createdOptions.push(optionResult.rows[0]);

          // Insert option values
          if (option.values && option.values.length > 0) {
            for (const value of option.values) {
              const valueId = `optval_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

              const insertValueQuery = `
                INSERT INTO product_option_value (
                  id, value, option_id, metadata, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, NOW(), NOW())
              `;

              await client.query(insertValueQuery, [
                valueId,
                value,
                optionId,
                JSON.stringify({ tenant_id: tenantId }),
              ]);
            }
          }
        }
        console.log(`✅ [TENANT FILTER] Created ${createdOptions.length} product options`);
      }

      // 3. Insert product variants
      if (productWithTenant.variants && productWithTenant.variants.length > 0) {
        for (const variant of productWithTenant.variants) {
          const variantId = `variant_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

          const insertVariantQuery = `
            INSERT INTO product_variant (
              id, title, sku, product_id, tenant_id, 
              metadata, weight, width, length, height,
              created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW(), NOW())
            RETURNING *
          `;

          const variantMetadata = {
            ...variant.metadata,
            tenant_id: tenantId,
            sale_price: variant.metadata?.sale_price,
            original_price: variant.metadata?.original_price,
            product_quantity: variant.metadata?.product_quantity,
            product_inventory_status: variant.metadata?.product_inventory_status,
          };

          const variantValues = [
            variantId,
            variant.title || 'Default',
            variant.sku || `sku_${productId}`,
            productId,
            tenantId,
            JSON.stringify(variantMetadata),
            variant.weight || null,
            variant.width || null,
            variant.length || null,
            variant.height || null,
          ];

          const variantResult = await client.query(insertVariantQuery, variantValues);
          createdVariants.push(variantResult.rows[0]);

          // Insert variant prices (Medusa v2 uses price_set structure)
          if (variant.prices && variant.prices.length > 0) {
            // First create a price set for this variant
            const priceSetId = `pset_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

            const insertPriceSetQuery = `
              INSERT INTO price_set (
                id, created_at, updated_at
              ) VALUES ($1, NOW(), NOW())
            `;
            await client.query(insertPriceSetQuery, [priceSetId]);

            // Link variant to price set
            const linkVariantPriceSetQuery = `
              INSERT INTO product_variant_price_set (
                id, variant_id, price_set_id, created_at, updated_at
              ) VALUES ($1, $2, $3, NOW(), NOW())
            `;
            const linkId = `pvps_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
            await client.query(linkVariantPriceSetQuery, [linkId, variantId, priceSetId]);

            // Add prices to the price set
            for (const price of variant.prices) {
              const priceId = `price_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

              const insertPriceQuery = `
                INSERT INTO price (
                  id, currency_code, amount, raw_amount, price_set_id, tenant_id, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
              `;

              await client.query(insertPriceQuery, [
                priceId,
                price.currency_code || 'inr',
                price.amount,
                JSON.stringify({ value: price.amount.toString(), precision: 20 }),
                priceSetId,
                tenantId,
              ]);
            }
          }
        }
        console.log(`✅ [TENANT FILTER] Created ${createdVariants.length} product variants`);
      }

      // 4. Insert product images
      if (productWithTenant.images && productWithTenant.images.length > 0) {
        for (const image of productWithTenant.images) {
          const imageId = `img_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

          const insertImageQuery = `
            INSERT INTO image (
              id, url, product_id, metadata, rank, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
            RETURNING *
          `;

          const imageValues = [
            imageId,
            image.url,
            productId,
            JSON.stringify({ tenant_id: tenantId }),
            0, // rank is required
          ];

          const imageResult = await client.query(insertImageQuery, imageValues);
          createdImages.push(imageResult.rows[0]);
        }
        console.log(`✅ [TENANT FILTER] Created ${createdImages.length} product images`);
      }

      // 5. Handle product categories (if provided)
      if (productWithTenant.categories && productWithTenant.categories.length > 0) {
        for (const category of productWithTenant.categories) {
          // Handle both string IDs and objects with id property
          const categoryId = typeof category === 'string' ? category : category.id;

          if (!categoryId) {
            console.warn(`⚠️ [TENANT FILTER] Skipping invalid category:`, category);
            continue;
          }

          const insertCategoryLinkQuery = `
            INSERT INTO product_category_product (
              product_id, product_category_id, tenant_id, created_at, updated_at
            ) VALUES ($1, $2, $3, NOW(), NOW())
          `;

          await client.query(insertCategoryLinkQuery, [productId, categoryId, tenantId]);
        }
        console.log(
          `✅ [TENANT FILTER] Linked product to ${productWithTenant.categories.length} categories`
        );
      }

      // 6. Handle product tags (if provided)
      if (productWithTenant.tags && productWithTenant.tags.length > 0) {
        for (const tag of productWithTenant.tags) {
          // First ensure tag exists or create it
          const tagId = `tag_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

          const insertTagQuery = `
            INSERT INTO product_tag (
              id, value, tenant_id, created_at, updated_at
            ) VALUES ($1, $2, $3, NOW(), NOW())
            ON CONFLICT (value, tenant_id) DO NOTHING
          `;

          await client.query(insertTagQuery, [tagId, tag, tenantId]);

          // Link tag to product
          const insertTagLinkQuery = `
            INSERT INTO product_tags (
              product_id, product_tag_id, tenant_id, created_at
            ) VALUES ($1, (SELECT id FROM product_tag WHERE value = $2 AND tenant_id = $3), $3, NOW())
          `;

          await client.query(insertTagLinkQuery, [productId, tag, tenantId]);
        }
        console.log(`✅ [TENANT FILTER] Linked product to ${productWithTenant.tags.length} tags`);
      }

      // Commit transaction
      await client.query('COMMIT');
      console.log(`✅ [TENANT FILTER] Transaction committed successfully`);

      await client.end();
    } catch (dbError) {
      console.error('❌ [TENANT FILTER] Database error:', dbError);
      await client.query('ROLLBACK').catch(() => {});
      await client.end().catch(() => {});
      throw dbError;
    }

    // Build comprehensive response
    const response = {
      product: {
        ...createdProduct,
        variants: createdVariants,
        images: createdImages,
        options: createdOptions,
        categories: productWithTenant.categories || [],
        tags: productWithTenant.tags || [],
      },
      _tenant: {
        id: tenantId,
        injected: true,
        method: 'comprehensive_db_creation',
        entities_created: {
          product: 1,
          variants: createdVariants.length,
          images: createdImages.length,
          options: createdOptions.length,
          categories: productWithTenant.categories?.length || 0,
          tags: productWithTenant.tags?.length || 0,
        },
      },
    };

    // Add tenant headers
    res.setHeader('X-Tenant-ID', tenantId);
    res.setHeader('X-Tenant-Injected', 'true');
    res.setHeader('X-Entities-Created', JSON.stringify(response._tenant.entities_created));

    console.log(`📤 [TENANT FILTER] Returning comprehensive product for tenant ${tenantId}`);
    console.log(`📊 [TENANT FILTER] Entities created:`, response._tenant.entities_created);

    res.status(201).json(response);
  } catch (error: any) {
    console.error('❌ [TENANT FILTER] Error creating comprehensive product:', error);

    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    res.status(500).json({
      error: 'Failed to create comprehensive product',
      message: error.message,
      tenant_id: tenantId,
      _debug: {
        error_type: 'tenant_comprehensive_create_error',
        timestamp: new Date().toISOString(),
        stack: error.stack,
      },
    });
  }
}

// PUT method removed - Medusa v2 uses POST to /admin/products/:id for updates
